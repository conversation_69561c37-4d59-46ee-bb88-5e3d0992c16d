jupiter_add_market_url = "http://208.91.110.251:8081/add-market"
csv_file_path = "mints.csv"
request_delay_ms = 1000

[rpc]
urls = [
    "https://mainnet.helius-rpc.com/?api-key=cb9797b4-0332-47f2-9857-ab38547a3a56",
    "https://tiniest-proportionate-breeze.solana-mainnet.quiknode.pro/9d6c36d5a0ae60f265d3299903305b29f1194968/",
    "https://solana-mainnet.g.alchemy.com/v2/********************************"
]
timeout_secs = 60
initial_connection_timeout_secs = 30

[dex_program_ids]
raydium_clmm = "CAMMCzo5YL8w4VFF8KVHrK22GGUsp5VTaW7grrKgrWqK" # Raydium CLMM Program ID
pump_amm = "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P"       # Pump AMM Program ID (for bonding curve PDA)
meteora_dlmm = "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo"    # Meteora DLMM Program ID
meteora_amm = "Eo7WjKq67rjJQSZxS6z3YkapzY3eMj6Xy8X5EQVn5UaB"     # Meteora AMM Program ID
raydium_amm_v4 = "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8" # Raydium AMM v4 Program ID
raydium_cpmm = "CPMMoo8L3F4NbTegBCKVNunggL7H1ZpdTHKxQB5qKP1C"   # Raydium CPMM Program ID 