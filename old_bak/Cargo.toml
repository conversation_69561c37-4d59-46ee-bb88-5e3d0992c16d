[package]
name = "market-adder"
version = "0.1.0"
edition = "2021"

[dependencies]
tokio = { version = "1", features = ["full"] }
reqwest = { version = "0.11", features = ["json"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
csv = "1.1"
solana-client = "1.18.26"
solana-sdk = "1.18.26"
solana-program = "1.18.26"
solana-account-decoder = "1.18.26"
serum_dex = "0.5.4"
bs58 = "0.4"
thiserror = "1.0"
bytemuck = { version = "1.14", features = ["derive"] }
toml = "0.8" 