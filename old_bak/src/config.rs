use serde::Deserialize;
use std::fs;
use std::path::Path;
use crate::types::AppError;

#[derive(Debug, Deserialize)]
pub struct RpcConfig {
    pub urls: Vec<String>,
    pub timeout_secs: Option<u64>, // Timeout for individual RPC requests
    pub initial_connection_timeout_secs: Option<u64>, // Timeout for the first health check
}

#[derive(Debug, Deserialize)]
pub struct DexIdConfig {
    pub raydium_clmm: Option<String>,
    pub meteora_dlmm: Option<String>,
    pub pump_amm: Option<String>,
    pub meteora_amm: Option<String>,
    pub raydium_amm_v4: Option<String>,
    pub raydium_cpmm: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct Settings {
    pub rpc: RpcConfig,
    pub jupiter_add_market_url: String,
    pub csv_file_path: String,
    pub dex_program_ids: DexIdConfig,
    pub request_delay_ms: Option<u64>, // Delay between processing token pairs in main loop
}

impl Settings {
    pub fn load(config_path_str: &str) -> Result<Self, AppError> {
        let config_path = Path::new(config_path_str);
        if !config_path.exists() {
            return Err(AppError::IoError(std::io::Error::new(
                std::io::ErrorKind::NotFound,
                format!("Configuration file not found: {}", config_path_str),
            )));
        }

        let config_content = fs::read_to_string(config_path)
            .map_err(|e| AppError::IoError(e))?;
        
        toml::from_str(&config_content)
            .map_err(|e| AppError::ConversionError(format!("Failed to parse TOML config: {}", e)))
    }
}

// 默认值函数，如果需要的话
impl Default for Settings {
    fn default() -> Self {
        Settings {
            rpc: RpcConfig {
                urls: vec![
                    "https://mainnet.helius-rpc.com/?api-key=YOUR_HELIUS_KEY".to_string(), // Placeholder
                ],
                timeout_secs: Some(60),
                initial_connection_timeout_secs: Some(30),
            },
            jupiter_add_market_url: "http://localhost:8081/add-market".to_string(), // Placeholder, use actual if known
            csv_file_path: "mints.csv".to_string(),
            dex_program_ids: DexIdConfig {
                raydium_clmm: Some("CAMMCzo5YL8w4VFF8KVHrK22GGUsp5VTaW7grrKgrWqK".to_string()), // Default Raydium CLMM
                meteora_dlmm: None,
                pump_amm: None,
                meteora_amm: None,
                raydium_amm_v4: None,
                raydium_cpmm: None,
            },
            request_delay_ms: Some(1000),
        }
    }
}

// 可以添加一个函数来获取特定DEX的Program ID
impl DexIdConfig {
    pub fn get_id(&self, dex_type: &str) -> Option<String> {
        match dex_type.to_lowercase().as_str() {
            "raydium_clmm" => self.raydium_clmm.clone(),
            "meteora_dlmm" => self.meteora_dlmm.clone(),
            "pump_amm" => self.pump_amm.clone(),
            "meteora_amm" => self.meteora_amm.clone(),
            "raydium_amm_v4" => self.raydium_amm_v4.clone(),
            "raydium_cpmm" => self.raydium_cpmm.clone(),
            _ => None,
        }
    }
} 