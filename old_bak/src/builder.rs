// 构建 add-market 请求逻辑 

use crate::types::{AddMarketRequest, AddMarketParams};

pub fn build_add_market_request(
    market_address: &str,
    owner_program_id: &str,
    params: Option<AddMarketParams>,
    // lookup_table_address: Option<String>, // 保持简单，暂时不传入
) -> AddMarketRequest {
    AddMarketRequest {
        address: market_address.to_string(),
        owner: owner_program_id.to_string(),
        params,
        address_lookup_table_address: None, // 根据PRD，此字段可选，暂时设为 None
    }
} 