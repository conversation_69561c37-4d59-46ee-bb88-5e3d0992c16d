// 链上查询逻辑 

use solana_client::rpc_client::RpcClient;
use solana_sdk::pubkey::Pubkey as SolanaPubkey; // Renamed to avoid conflict if serum_dex::state::Pubkey is brought into scope directly
use serum_dex::state::{MarketState, AccountFlag as SerumAccountFlag};
use crate::types::{AppError, AddMarketParams};
use std::str::FromStr;
use bytemuck;

// This function is unused and can be removed.
// const SERUM_DEX_PROGRAM_ID: &str = "9xQeWvG816bUx9EPjHmaT23yvVM2ZWbrrpZb9PusVFin"; 
// fn get_serum_program_id(market_address_str: &str) -> Result<SolanaPubkey, AppError> {
//     Pubkey::from_str(SERUM_DEX_PROGRAM_ID).map_err(|e| AppError::SolanaSdkError(format!("Invalid Serum DEX Program ID: {}", e)))
// }

pub fn fetch_serum_market_details(rpc_client: &RpcClient, market_address_str: &str) -> Result<(AddMarketParams, String), AppError> {
    let market_address = SolanaPubkey::from_str(market_address_str)
        .map_err(|e| AppError::SolanaSdkError(format!("Invalid market address '{}': {}", market_address_str, e)))?;

    let market_account = rpc_client.get_account(&market_address)?;
    let market_account_data = &market_account.data;
    let owner_program_id = market_account.owner;

    println!(
        "  市场地址 {} 的账户数据长度: {}, Owner Program: {}",
        market_address_str,
        market_account_data.len(),
        owner_program_id
    );

    // Deserialize MarketState from account data using bytemuck
    let market_state: &MarketState = bytemuck::try_from_bytes(market_account_data)
        .map_err(|e| AppError::SerumDexError(format!("Failed to cast market account data for {}: {:?}", market_address_str, e)))?;

    // Copy fields from packed struct to local variables to avoid unaligned references
    let account_flags = market_state.account_flags;
    let asks_pk_data = market_state.asks; // Assuming this is [u64; 4] or similar Pod type
    let bids_pk_data = market_state.bids;
    let event_q_pk_data = market_state.event_q; // 获取 event_q 数据
    let coin_vault_pk_data = market_state.coin_vault;
    let pc_vault_pk_data = market_state.pc_vault;
    let vault_signer_nonce = market_state.vault_signer_nonce;

    // Check account flags using bitwise operations
    let required_flags = SerumAccountFlag::Initialized as u64 | SerumAccountFlag::Market as u64;
    if (account_flags & required_flags) != required_flags {
        return Err(AppError::InvalidMarketData(format!(
            "Market {} is not initialized or not a market account. Flags: {:?}",
            market_address_str,
            account_flags // Use the copied value
        )));
    }

    let vault_signer_address = SolanaPubkey::create_program_address(
        &[
            market_address.as_ref(),
            &vault_signer_nonce.to_le_bytes(), // Use the copied value
        ],
        &owner_program_id,
    ).map_err(|e| AppError::SerumDexError(format!("Failed to create vault signer PDA for {}: {}", market_address_str, e)))?;

    let params = AddMarketParams {
        serum_asks: Some(SolanaPubkey::new_from_array(bytemuck::cast(asks_pk_data)).to_string()),
        serum_bids: Some(SolanaPubkey::new_from_array(bytemuck::cast(bids_pk_data)).to_string()),
        serum_coin_vault_account: Some(SolanaPubkey::new_from_array(bytemuck::cast(coin_vault_pk_data)).to_string()),
        serum_event_queue: Some(SolanaPubkey::new_from_array(bytemuck::cast(event_q_pk_data)).to_string()), // 添加 event_queue
        serum_pc_vault_account: Some(SolanaPubkey::new_from_array(bytemuck::cast(pc_vault_pk_data)).to_string()),
        serum_vault_signer: Some(vault_signer_address.to_string()),
    };

    Ok((params, owner_program_id.to_string()))
} 