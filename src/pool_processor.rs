use crate::jupiter_formatter::{<PERSON>Formatter, JupiterMarket};
use std::error::Error;
use std::fs;

pub struct PoolProcessor {
    formatter: JupiterFormatter,
}

impl PoolProcessor {
    pub fn new() -> Self {
        Self {
            formatter: JupiterFormatter::new(),
        }
    }

    pub async fn process_all_pools(&self) -> Result<(), Box<dyn Error>> {
        println!("开始处理所有池子信息...");
        println!("================================");

        // Process Raydium V3 pools
        let raydium_v3_data = fs::read_to_string("/tmp/raydium_v3_pools.json")?;
        let raydium_v3_markets = self.formatter.format_raydium_v3_pools(&raydium_v3_data)?;
        self.formatter.print_market_details(&raydium_v3_markets, "Raydium V3");

        // Process Meteora DLMM pools
        let meteora_dlmm_data = fs::read_to_string("/tmp/meteora_dlmm_pools.json")?;
        let meteora_dlmm_markets = self.formatter.format_meteora_dlmm_pools(&meteora_dlmm_data)?;
        self.formatter.print_market_details(&meteora_dlmm_markets, "Meteora DLMM");

        // Process Raydium V2 CLMM pools
        let raydium_v2_data = fs::read_to_string("/tmp/raydium_v2_clmm_pools.json")?;
        let raydium_v2_markets = self.formatter.format_raydium_v2_clmm_pools(&raydium_v2_data)?;
        self.formatter.print_market_details(&raydium_v2_markets, "Raydium V2 CLMM");

        // Combine all markets
        let mut all_markets = Vec::new();
        all_markets.extend(raydium_v3_markets);
        all_markets.extend(meteora_dlmm_markets);
        all_markets.extend(raydium_v2_markets);

        println!("总计市场数量: {}", all_markets.len());
        println!("================================");

        // Submit to Jupiter
        println!("开始提交到 Jupiter API...");
        self.formatter.submit_to_jupiter(&all_markets).await?;

        Ok(())
    }

    pub fn save_markets_json(&self, markets: &[JupiterMarket], filename: &str) -> Result<(), Box<dyn Error>> {
        let json = serde_json::to_string_pretty(markets)?;
        fs::write(filename, json)?;
        println!("保存市场信息到: {}", filename);
        Ok(())
    }
}