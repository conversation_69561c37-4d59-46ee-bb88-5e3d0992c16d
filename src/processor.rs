use crate::config::Config;
use crate::dex_client::DexClient;
use crate::jupiter_client::JupiterClient;
use crate::logger::Logger;
use crate::pool_filter::PoolFilter;
use crate::types::{MintPair, ProcessingReport};
use anyhow::{Context, Result};
use std::fs::File;
use tracing::info;

pub struct Processor {
    config: Config,
    dex_client: DexClient,
    jupiter_client: JupiterClient,
    logger: Logger,
    pool_filter: PoolFilter,
}

impl Processor {
    pub fn new(config: Config) -> Self {
        let dex_client = DexClient::new(config.clone());
        let jupiter_client = JupiterClient::new(config.jupiter.clone());
        let logger = Logger::new(config.logging.clone());
        let pool_filter = PoolFilter::new(config.filters.clone());

        Self {
            config,
            dex_client,
            jupiter_client,
            logger,
            pool_filter,
        }
    }

    /// 初始化处理器
    pub fn init(&self) -> Result<()> {
        self.logger.init()?;
        
        // 显示过滤器配置
        let filter_stats = self.pool_filter.get_filter_stats();
        filter_stats.print_summary();
        
        info!("处理器初始化完成");
        Ok(())
    }

    /// 执行完整的处理流程
    pub async fn run(&self) -> Result<ProcessingReport> {
        info!("开始执行Jupiter Market Discovery处理流程");

        // 1. 加载CSV文件
        let mint_pairs = self.load_mint_pairs().await?;
        info!("加载了 {} 个mint对", mint_pairs.len());

        // 2. 获取所有DEX的池信息
        let pools = self.dex_client.get_all_pools(&mint_pairs).await?;
        info!("从各DEX获取到 {} 个池", pools.len());

        if pools.is_empty() {
            info!("未找到任何池，结束处理");
            return Ok(ProcessingReport {
                total_mint_pairs: mint_pairs.len(),
                total_pools_found: 0,
                total_submissions: 0,
                successful_submissions: 0,
                failed_submissions: 0,
                results: Vec::new(),
            });
        }

        // 3. 应用池过滤器
        let filtered_pools = self.pool_filter.filter_pools(pools);
        info!("过滤后剩余 {} 个高质量池", filtered_pools.len());

        if filtered_pools.is_empty() {
            info!("所有池都被过滤器拒绝，结束处理");
            return Ok(ProcessingReport {
                total_mint_pairs: mint_pairs.len(),
                total_pools_found: 0,
                total_submissions: 0,
                successful_submissions: 0,
                failed_submissions: 0,
                results: Vec::new(),
            });
        }

        // 4. 提交到Jupiter
        let submission_results = self.jupiter_client.submit_markets(filtered_pools.clone()).await?;
        
        // 5. 统计结果
        let successful_submissions = submission_results.iter()
            .filter(|r| r.success)
            .count();
        let failed_submissions = submission_results.len() - successful_submissions;

        // 6. 记录日志
        self.logger.log_batch_results(&submission_results)?;

        // 7. 验证提交结果
        self.verify_submissions(&mint_pairs).await?;

        // 8. 生成报告
        let report = ProcessingReport {
            total_mint_pairs: mint_pairs.len(),
            total_pools_found: filtered_pools.len(),
            total_submissions: submission_results.len(),
            successful_submissions,
            failed_submissions,
            results: submission_results,
        };

        self.logger.log_report(&report)?;
        self.print_summary(&report);

        info!("处理流程完成");
        Ok(report)
    }

    /// 加载CSV文件中的mint对
    async fn load_mint_pairs(&self) -> Result<Vec<MintPair>> {
        let file = File::open(&self.config.processing.csv_file_path)
            .with_context(|| format!("无法打开CSV文件: {}", self.config.processing.csv_file_path))?;

        let mut reader = csv::Reader::from_reader(file);
        let mut mint_pairs = Vec::new();

        for result in reader.deserialize() {
            let pair: MintPair = result
                .with_context(|| "解析CSV行失败")?;
            mint_pairs.push(pair);
        }

        if mint_pairs.is_empty() {
            anyhow::bail!("CSV文件中没有有效的mint对");
        }

        Ok(mint_pairs)
    }

    /// 验证提交结果
    async fn verify_submissions(&self, mint_pairs: &[MintPair]) -> Result<()> {
        info!("开始验证提交结果...");

        for (idx, api_url) in self.config.jupiter.api_urls.iter().enumerate() {
            info!("验证Jupiter实例 {} ({})", idx + 1, api_url);

            for pair in mint_pairs {
                match self.jupiter_client.verify_submission(
                    &pair.base_mint,
                    &pair.quote_mint,
                    api_url,
                ).await {
                    Ok(true) => {
                        info!("✓ 验证成功: {} <-> {}", 
                              &pair.base_mint[..8], &pair.quote_mint[..8]);
                    }
                    Ok(false) => {
                        info!("⚠ 验证失败: {} <-> {} (可能需要时间同步)", 
                              &pair.base_mint[..8], &pair.quote_mint[..8]);
                    }
                    Err(e) => {
                        info!("⚠ 验证出错: {} <-> {} - {}", 
                              &pair.base_mint[..8], &pair.quote_mint[..8], e);
                    }
                }
            }
        }

        Ok(())
    }

    /// 打印处理摘要
    fn print_summary(&self, report: &ProcessingReport) {
        println!("\n{}", "=".repeat(60));
        println!("           Jupiter Market Discovery 处理报告");
        println!("{}", "=".repeat(60));
        println!("📊 统计信息:");
        println!("   • Mint对数量: {}", report.total_mint_pairs);
        println!("   • 发现池数量: {}", report.total_pools_found);
        println!("   • 提交总数: {}", report.total_submissions);
        println!("   • 成功提交: {} ✓", report.successful_submissions);
        println!("   • 失败提交: {} ✗", report.failed_submissions);
        
        let success_rate = if report.total_submissions > 0 {
            (report.successful_submissions as f64 / report.total_submissions as f64) * 100.0
        } else {
            0.0
        };
        println!("   • 成功率: {:.1}%", success_rate);

        println!("\n📁 日志文件:");
        println!("   • 成功日志: {}", self.config.logging.success_log_file);
        println!("   • 错误日志: {}", self.config.logging.error_log_file);

        println!("\n🎯 Jupiter实例:");
        for (idx, url) in self.config.jupiter.api_urls.iter().enumerate() {
            println!("   {}. {}", idx + 1, url);
        }

        println!("{}", "=".repeat(60));

        if report.failed_submissions > 0 {
            println!("⚠️  有 {} 个提交失败，请检查错误日志", report.failed_submissions);
        } else {
            println!("🎉 所有提交都成功完成！");
        }
    }
}