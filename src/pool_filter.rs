use crate::config::FilterConfig;
use crate::types::PoolInfo;
use std::collections::HashSet;
use tracing::{debug, info, warn};

/// 池过滤结果
#[derive(Debug, Clone)]
pub enum FilterResult {
    Approved,
    Rejected(String),
}

impl FilterResult {
    pub fn is_approved(&self) -> bool {
        matches!(self, FilterResult::Approved)
    }

    pub fn rejection_reason(&self) -> Option<&str> {
        match self {
            FilterResult::Rejected(reason) => Some(reason),
            FilterResult::Approved => None,
        }
    }
}

/// 池质量过滤器
pub struct PoolFilter {
    config: FilterConfig,
    blacklisted_tokens: HashSet<String>,
    supported_program_ids: HashSet<String>,
}

impl PoolFilter {
    pub fn new(config: FilterConfig) -> Self {
        let blacklisted_tokens = config.blacklisted_tokens
            .iter()
            .cloned()
            .collect();

        let supported_program_ids = config.supported_program_ids
            .iter()
            .cloned()
            .collect();

        Self {
            config,
            blacklisted_tokens,
            supported_program_ids,
        }
    }

    /// 批量过滤池信息
    pub fn filter_pools(&self, pools: Vec<PoolInfo>) -> Vec<PoolInfo> {
        let initial_count = pools.len();
        info!("开始过滤 {} 个池", initial_count);

        let filtered_pools: Vec<PoolInfo> = pools
            .into_iter()
            .filter(|pool| {
                let result = self.validate_pool(pool);
                match result {
                    FilterResult::Approved => {
                        debug!("✓ 池通过验证: {} ({})", pool.address, pool.dex_name);
                        true
                    }
                    FilterResult::Rejected(reason) => {
                        debug!("✗ 池被拒绝: {} - {}", pool.address, reason);
                        false
                    }
                }
            })
            .collect();

        let filtered_count = filtered_pools.len();
        let rejected_count = initial_count - filtered_count;
        
        info!("过滤完成: {} 个池通过验证，{} 个池被拒绝", 
              filtered_count, rejected_count);

        if rejected_count > 0 {
            let rejection_rate = (rejected_count as f64 / initial_count as f64) * 100.0;
            info!("拒绝率: {:.1}%", rejection_rate);
        }

        filtered_pools
    }

    /// 验证单个池
    pub fn validate_pool(&self, pool: &PoolInfo) -> FilterResult {
        // 1. 流动性检查
        if let Some(result) = self.check_liquidity(pool) {
            if !result.is_approved() {
                return result;
            }
        }

        // 2. 交易量检查
        if let Some(result) = self.check_volume(pool) {
            if !result.is_approved() {
                return result;
            }
        }

        // 3. 黑名单检查
        if let Some(result) = self.check_blacklist(pool) {
            if !result.is_approved() {
                return result;
            }
        }

        // 4. 程序ID检查
        if let Some(result) = self.check_program_id(pool) {
            if !result.is_approved() {
                return result;
            }
        }

        // 5. 技术兼容性检查
        if let Some(result) = self.check_technical_compatibility(pool) {
            if !result.is_approved() {
                return result;
            }
        }

        FilterResult::Approved
    }

    /// 检查流动性
    fn check_liquidity(&self, pool: &PoolInfo) -> Option<FilterResult> {
        if !self.config.enable_liquidity_filter {
            return None;
        }

        match pool.liquidity_usd {
            Some(liquidity) => {
                if liquidity < self.config.min_liquidity_usd {
                    Some(FilterResult::Rejected(
                        format!("流动性过低: ${:.2} < ${:.2}", 
                               liquidity, self.config.min_liquidity_usd)
                    ))
                } else {
                    None
                }
            }
            None => {
                if self.config.require_liquidity_data {
                    Some(FilterResult::Rejected("缺少流动性数据".to_string()))
                } else {
                    warn!("池 {} 缺少流动性数据，但配置允许通过", pool.address);
                    None
                }
            }
        }
    }

    /// 检查交易量
    fn check_volume(&self, pool: &PoolInfo) -> Option<FilterResult> {
        if !self.config.enable_volume_filter {
            return None;
        }

        match pool.volume_24h_usd {
            Some(volume) => {
                if volume < self.config.min_volume_24h_usd {
                    Some(FilterResult::Rejected(
                        format!("24小时交易量过低: ${:.2} < ${:.2}", 
                               volume, self.config.min_volume_24h_usd)
                    ))
                } else {
                    None
                }
            }
            None => {
                if self.config.require_volume_data {
                    Some(FilterResult::Rejected("缺少交易量数据".to_string()))
                } else {
                    warn!("池 {} 缺少交易量数据，但配置允许通过", pool.address);
                    None
                }
            }
        }
    }

    /// 检查黑名单
    fn check_blacklist(&self, pool: &PoolInfo) -> Option<FilterResult> {
        if !self.config.enable_blacklist_filter {
            return None;
        }

        if self.blacklisted_tokens.contains(&pool.base_mint) {
            Some(FilterResult::Rejected(
                format!("基础代币在黑名单中: {}", pool.base_mint)
            ))
        } else if self.blacklisted_tokens.contains(&pool.quote_mint) {
            Some(FilterResult::Rejected(
                format!("报价代币在黑名单中: {}", pool.quote_mint)
            ))
        } else {
            None
        }
    }

    /// 检查程序ID
    fn check_program_id(&self, pool: &PoolInfo) -> Option<FilterResult> {
        if !self.config.enable_program_id_filter {
            return None;
        }

        if self.supported_program_ids.is_empty() {
            // 如果没有配置支持的程序ID，则跳过检查
            return None;
        }

        if !self.supported_program_ids.contains(&pool.owner) {
            Some(FilterResult::Rejected(
                format!("不支持的程序ID: {}", pool.owner)
            ))
        } else {
            None
        }
    }

    /// 检查技术兼容性
    fn check_technical_compatibility(&self, pool: &PoolInfo) -> Option<FilterResult> {
        // 检查地址格式
        if pool.address.len() < 32 || pool.address.len() > 44 {
            return Some(FilterResult::Rejected(
                "池地址格式无效".to_string()
            ));
        }

        // 检查程序ID格式
        if pool.owner.len() < 32 || pool.owner.len() > 44 {
            return Some(FilterResult::Rejected(
                "程序ID格式无效".to_string()
            ));
        }

        // 检查mint地址格式
        if pool.base_mint.len() < 32 || pool.base_mint.len() > 44 {
            return Some(FilterResult::Rejected(
                "基础代币mint地址格式无效".to_string()
            ));
        }

        if pool.quote_mint.len() < 32 || pool.quote_mint.len() > 44 {
            return Some(FilterResult::Rejected(
                "报价代币mint地址格式无效".to_string()
            ));
        }

        // 检查mint地址不能相同
        if pool.base_mint == pool.quote_mint {
            return Some(FilterResult::Rejected(
                "基础代币和报价代币不能相同".to_string()
            ));
        }

        None
    }

    /// 获取过滤统计信息
    pub fn get_filter_stats(&self) -> FilterStats {
        FilterStats {
            liquidity_filter_enabled: self.config.enable_liquidity_filter,
            min_liquidity_usd: self.config.min_liquidity_usd,
            volume_filter_enabled: self.config.enable_volume_filter,
            min_volume_24h_usd: self.config.min_volume_24h_usd,
            blacklist_filter_enabled: self.config.enable_blacklist_filter,
            blacklisted_tokens_count: self.blacklisted_tokens.len(),
            program_id_filter_enabled: self.config.enable_program_id_filter,
            supported_program_ids_count: self.supported_program_ids.len(),
        }
    }
}

/// 过滤器统计信息
#[derive(Debug)]
pub struct FilterStats {
    pub liquidity_filter_enabled: bool,
    pub min_liquidity_usd: f64,
    pub volume_filter_enabled: bool,
    pub min_volume_24h_usd: f64,
    pub blacklist_filter_enabled: bool,
    pub blacklisted_tokens_count: usize,
    pub program_id_filter_enabled: bool,
    pub supported_program_ids_count: usize,
}

impl FilterStats {
    pub fn print_summary(&self) {
        println!("\n📋 池过滤器配置摘要:");
        println!("==================");
        
        if self.liquidity_filter_enabled {
            println!("✓ 流动性过滤: 最低 ${:.0}", self.min_liquidity_usd);
        } else {
            println!("✗ 流动性过滤: 已禁用");
        }
        
        if self.volume_filter_enabled {
            println!("✓ 交易量过滤: 最低 ${:.0}/24h", self.min_volume_24h_usd);
        } else {
            println!("✗ 交易量过滤: 已禁用");
        }
        
        if self.blacklist_filter_enabled {
            println!("✓ 黑名单过滤: {} 个代币", self.blacklisted_tokens_count);
        } else {
            println!("✗ 黑名单过滤: 已禁用");
        }
        
        if self.program_id_filter_enabled {
            println!("✓ 程序ID过滤: {} 个支持的程序", self.supported_program_ids_count);
        } else {
            println!("✗ 程序ID过滤: 已禁用");
        }
    }
}