use serde::{Deserialize, Serialize};

/// CSV中的mint对
#[derive(Debug, <PERSON>lone, Deserialize)]
pub struct MintPair {
    pub base_mint: String,
    pub quote_mint: String,
}

/// 从DEX API获取的池信息
#[derive(Debug, Clone)]
pub struct PoolInfo {
    pub address: String,
    pub owner: String,
    pub base_mint: String,
    pub quote_mint: String,
    pub dex_name: String,
    pub pool_type: Option<String>,
    pub liquidity_usd: Option<f64>,
    pub volume_24h_usd: Option<f64>,
}

/// Jupiter API期望的格式
#[derive(Debug, Clone, Serialize)]
pub struct JupiterMarket {
    pub address: String,
    pub owner: String,
    pub params: Option<serde_json::Value>,
    #[serde(rename = "addressLookupTableAddress")]
    pub address_lookup_table_address: Option<String>,
}

/// Raydium V3 API响应
#[derive(Debug, Deserialize)]
pub struct RaydiumV3Response {
    pub success: bool,
    pub data: RaydiumV3Data,
}

#[derive(Debug, Deserialize)]
pub struct RaydiumV3Data {
    pub count: u32,
    pub data: Vec<RaydiumV3Pool>,
}

#[derive(Debug, Deserialize)]
pub struct RaydiumV3Pool {
    pub id: String,
    #[serde(rename = "programId")]
    pub program_id: String,
    #[serde(rename = "type")]
    pub pool_type: String,
    #[serde(rename = "mintA")]
    pub mint_a: RaydiumMint,
    #[serde(rename = "mintB")]
    pub mint_b: RaydiumMint,
    pub liquidity: Option<String>,
    pub tvl: Option<f64>,  // 实际的流动性数据字段
    pub day: Option<RaydiumStats>,
}

#[derive(Debug, Deserialize)]
pub struct RaydiumMint {
    pub address: String,
    pub symbol: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct RaydiumStats {
    pub volume: Option<f64>,
    #[serde(rename = "volumeQuote")]
    pub volume_quote: Option<f64>,
}

/// Raydium V2 CLMM Pool
#[derive(Debug, Deserialize)]
pub struct RaydiumV2Pool {
    pub id: String,
    #[serde(rename = "mintA")]
    pub mint_a: String,
    #[serde(rename = "mintB")]
    pub mint_b: String,
    #[serde(rename = "lookupTableAccount")]
    pub lookup_table_account: Option<String>,
    pub tvl: Option<f64>,
}

/// Meteora DLMM Pool
#[derive(Debug, Deserialize)]
pub struct MeteoraPool {
    pub address: String,
    pub mint_x: String,
    pub mint_y: String,
    pub bin_step: Option<u16>,
    pub liquidity: Option<String>,
    pub trade_volume_24h: Option<f64>,  // 实际是f64不是String
    pub fees_24h: Option<f64>,         // 24小时手续费
    pub apr: Option<f64>,              // 年化收益率
}

/// 提交结果
#[derive(Debug)]
pub struct SubmissionResult {
    pub market: JupiterMarket,
    pub success: bool,
    pub error: Option<String>,
    pub jupiter_url: String,
}

/// 处理报告
#[derive(Debug)]
pub struct ProcessingReport {
    pub total_mint_pairs: usize,
    pub total_pools_found: usize,
    pub total_submissions: usize,
    pub successful_submissions: usize,
    pub failed_submissions: usize,
    pub results: Vec<SubmissionResult>,
}