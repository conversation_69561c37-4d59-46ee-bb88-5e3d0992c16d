use reqwest::Client;
use serde_json::Value;
use std::error::Error;

/// API测试模块
/// 验证各个DEX API端点的真实数据获取能力
pub struct ApiTester {
    client: Client,
}

impl ApiTester {
    pub fn new() -> Self {
        Self {
            client: Client::new(),
        }
    }

    /// 测试Raydium V3 API - 根据mint对查询池信息
    pub async fn test_raydium_v3_pools_by_mint(
        &self,
        mint1: &str,
        mint2: &str,
    ) -> Result<Value, Box<dyn Error>> {
        let url = format!(
            "https://api-v3.raydium.io/pools/info/mint?mint1={}&mint2={}&poolType=all&poolSortField=liquidity&sortType=desc&pageSize=100&page=1",
            mint1, mint2
        );

        println!("正在测试 Raydium V3 API: {}", url);

        let response = self.client.get(&url).send().await?;
        let status = response.status();

        if !status.is_success() {
            return Err(format!("Raydium V3 API 请求失败，状态码: {}", status).into());
        }

        let json_response: Value = response.json().await?;
        println!("Raydium V3 API 响应成功");
        
        Ok(json_response)
    }

    /// 测试Raydium V2 API - 获取主要交易对
    pub async fn test_raydium_v2_main_pairs(&self) -> Result<Value, Box<dyn Error>> {
        let url = "https://api.raydium.io/v2/main/pairs";

        println!("正在测试 Raydium V2 API: {}", url);

        let response = self.client.get(url).send().await?;
        let status = response.status();

        if !status.is_success() {
            return Err(format!("Raydium V2 API 请求失败，状态码: {}", status).into());
        }

        let json_response: Value = response.json().await?;
        println!("Raydium V2 API 响应成功");
        
        Ok(json_response)
    }

    /// 测试Raydium V2 CLMM API
    pub async fn test_raydium_v2_clmm_pools(&self) -> Result<Value, Box<dyn Error>> {
        let url = "https://api.raydium.io/v2/ammV3/ammPools";

        println!("正在测试 Raydium V2 CLMM API: {}", url);

        let response = self.client.get(url).send().await?;
        let status = response.status();

        if !status.is_success() {
            return Err(format!("Raydium V2 CLMM API 请求失败，状态码: {}", status).into());
        }

        let json_response: Value = response.json().await?;
        println!("Raydium V2 CLMM API 响应成功");
        
        Ok(json_response)
    }

    /// 测试Meteora DLMM API
    pub async fn test_meteora_dlmm_pairs(&self) -> Result<Value, Box<dyn Error>> {
        let url = "https://dlmm-api.meteora.ag/pair/all";

        println!("正在测试 Meteora DLMM API: {}", url);

        let response = self.client.get(url).send().await?;
        let status = response.status();

        if !status.is_success() {
            return Err(format!("Meteora DLMM API 请求失败，状态码: {}", status).into());
        }

        let json_response: Value = response.json().await?;
        println!("Meteora DLMM API 响应成功");
        
        Ok(json_response)
    }

    /// 测试Meteora AMM V2 API
    pub async fn test_meteora_amm_pools(&self) -> Result<Value, Box<dyn Error>> {
        let url = "https://amm-v2.meteora.ag/pools";

        println!("正在测试 Meteora AMM V2 API: {}", url);

        let response = self.client.get(url).send().await?;
        let status = response.status();

        if !status.is_success() {
            return Err(format!("Meteora AMM V2 API 请求失败，状态码: {}", status).into());
        }

        let json_response: Value = response.json().await?;
        println!("Meteora AMM V2 API 响应成功");
        
        Ok(json_response)
    }

    /// 查找特定mint对的市场信息
    pub async fn find_markets_for_mint_pair(
        &self,
        base_mint: &str,
        quote_mint: &str,
    ) -> Result<MarketSearchResults, Box<dyn Error>> {
        println!("开始查找 {}/{} 的市场信息", base_mint, quote_mint);

        let mut results = MarketSearchResults::new(base_mint, quote_mint);

        // 测试Raydium V3 API
        match self.test_raydium_v3_pools_by_mint(base_mint, quote_mint).await {
            Ok(response) => {
                results.raydium_v3_result = Some(response);
                println!("✓ Raydium V3 API 查询成功");
            }
            Err(e) => {
                println!("✗ Raydium V3 API 查询失败: {}", e);
                results.raydium_v3_error = Some(e.to_string());
            }
        }

        // 测试Raydium V2 主要交易对
        match self.test_raydium_v2_main_pairs().await {
            Ok(response) => {
                results.raydium_v2_pairs_result = Some(response);
                println!("✓ Raydium V2 交易对 API 查询成功");
            }
            Err(e) => {
                println!("✗ Raydium V2 交易对 API 查询失败: {}", e);
                results.raydium_v2_pairs_error = Some(e.to_string());
            }
        }

        // 测试Raydium V2 CLMM
        match self.test_raydium_v2_clmm_pools().await {
            Ok(response) => {
                results.raydium_v2_clmm_result = Some(response);
                println!("✓ Raydium V2 CLMM API 查询成功");
            }
            Err(e) => {
                println!("✗ Raydium V2 CLMM API 查询失败: {}", e);
                results.raydium_v2_clmm_error = Some(e.to_string());
            }
        }

        // 测试Meteora DLMM
        match self.test_meteora_dlmm_pairs().await {
            Ok(response) => {
                results.meteora_dlmm_result = Some(response);
                println!("✓ Meteora DLMM API 查询成功");
            }
            Err(e) => {
                println!("✗ Meteora DLMM API 查询失败: {}", e);
                results.meteora_dlmm_error = Some(e.to_string());
            }
        }

        // 测试Meteora AMM
        match self.test_meteora_amm_pools().await {
            Ok(response) => {
                results.meteora_amm_result = Some(response);
                println!("✓ Meteora AMM API 查询成功");
            }
            Err(e) => {
                println!("✗ Meteora AMM API 查询失败: {}", e);
                results.meteora_amm_error = Some(e.to_string());
            }
        }

        Ok(results)
    }
}

/// 市场搜索结果
#[derive(Debug)]
pub struct MarketSearchResults {
    pub base_mint: String,
    pub quote_mint: String,
    pub raydium_v3_result: Option<Value>,
    pub raydium_v3_error: Option<String>,
    pub raydium_v2_pairs_result: Option<Value>,
    pub raydium_v2_pairs_error: Option<String>,
    pub raydium_v2_clmm_result: Option<Value>,
    pub raydium_v2_clmm_error: Option<String>,
    pub meteora_dlmm_result: Option<Value>,
    pub meteora_dlmm_error: Option<String>,
    pub meteora_amm_result: Option<Value>,
    pub meteora_amm_error: Option<String>,
}

impl MarketSearchResults {
    pub fn new(base_mint: &str, quote_mint: &str) -> Self {
        Self {
            base_mint: base_mint.to_string(),
            quote_mint: quote_mint.to_string(),
            raydium_v3_result: None,
            raydium_v3_error: None,
            raydium_v2_pairs_result: None,
            raydium_v2_pairs_error: None,
            raydium_v2_clmm_result: None,
            raydium_v2_clmm_error: None,
            meteora_dlmm_result: None,
            meteora_dlmm_error: None,
            meteora_amm_result: None,
            meteora_amm_error: None,
        }
    }

    /// 打印搜索结果摘要
    pub fn print_summary(&self) {
        println!("\n=== 市场搜索结果摘要 for {}/{} ===", self.base_mint, self.quote_mint);
        
        println!("Raydium V3 API: {}", 
            if self.raydium_v3_result.is_some() { "✓ 成功" } else { "✗ 失败" });
        
        println!("Raydium V2 交易对 API: {}", 
            if self.raydium_v2_pairs_result.is_some() { "✓ 成功" } else { "✗ 失败" });
            
        println!("Raydium V2 CLMM API: {}", 
            if self.raydium_v2_clmm_result.is_some() { "✓ 成功" } else { "✗ 失败" });
        
        println!("Meteora DLMM API: {}", 
            if self.meteora_dlmm_result.is_some() { "✓ 成功" } else { "✗ 失败" });
        
        println!("Meteora AMM API: {}", 
            if self.meteora_amm_result.is_some() { "✓ 成功" } else { "✗ 失败" });
    }

    /// 分析找到的市场数据
    pub fn analyze_markets(&self) {
        println!("\n=== 市场数据分析 ===");

        // 分析Raydium V3结果
        if let Some(ref data) = self.raydium_v3_result {
            if let Some(success) = data.get("success").and_then(|v| v.as_bool()) {
                if success {
                    if let Some(pools_data) = data.get("data").and_then(|d| d.get("data")).and_then(|d| d.as_array()) {
                        println!("Raydium V3 找到 {} 个池", pools_data.len());
                        for (i, pool) in pools_data.iter().take(3).enumerate() {
                            if let (Some(pool_type), Some(pool_id)) = (
                                pool.get("type").and_then(|v| v.as_str()),
                                pool.get("id").and_then(|v| v.as_str())
                            ) {
                                println!("  池 {}: {} ({})", i + 1, pool_id, pool_type);
                            }
                        }
                    }
                }
            }
        }

        // 分析Meteora DLMM结果
        if let Some(ref data) = self.meteora_dlmm_result {
            if let Some(pairs) = data.as_array() {
                let matching_pairs: Vec<_> = pairs.iter().filter(|pair| {
                    if let (Some(mint_x), Some(mint_y)) = (
                        pair.get("mint_x").and_then(|v| v.as_str()),
                        pair.get("mint_y").and_then(|v| v.as_str())
                    ) {
                        (mint_x == self.base_mint && mint_y == self.quote_mint) ||
                        (mint_x == self.quote_mint && mint_y == self.base_mint)
                    } else {
                        false
                    }
                }).collect();

                println!("Meteora DLMM 找到 {} 个匹配的池", matching_pairs.len());
                for (i, pair) in matching_pairs.iter().take(3).enumerate() {
                    if let Some(address) = pair.get("address").and_then(|v| v.as_str()) {
                        println!("  池 {}: {}", i + 1, address);
                    }
                }
            }
        }
    }
}

/// 运行所有API测试
pub async fn run_all_api_tests() -> Result<(), Box<dyn Error>> {
    println!("开始运行所有DEX API测试...\n");

    let tester = ApiTester::new();

    // 使用示例mint对进行测试
    let base_mint = "Ey59PH7Z4BFU4HjyKnyMdWt5GGN76KazTAwQihoUXRnk";
    let quote_mint = "So11111111111111111111111111111111111111112"; // WSOL

    let results = tester.find_markets_for_mint_pair(base_mint, quote_mint).await?;
    
    results.print_summary();
    results.analyze_markets();

    println!("\n所有API测试完成！");
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_raydium_v3_api() {
        let tester = ApiTester::new();
        let result = tester.test_raydium_v3_pools_by_mint(
            "Ey59PH7Z4BFU4HjyKnyMdWt5GGN76KazTAwQihoUXRnk",
            "So11111111111111111111111111111111111111112"
        ).await;
        
        match result {
            Ok(data) => {
                println!("Raydium V3 API 测试成功: {}", serde_json::to_string_pretty(&data).unwrap());
                assert!(data.is_object());
            }
            Err(e) => {
                println!("Raydium V3 API 测试失败: {}", e);
                panic!("测试失败");
            }
        }
    }

    #[tokio::test] 
    async fn test_meteora_dlmm_api() {
        let tester = ApiTester::new();
        let result = tester.test_meteora_dlmm_pairs().await;
        
        match result {
            Ok(data) => {
                println!("Meteora DLMM API 测试成功");
                assert!(data.is_array());
            }
            Err(e) => {
                println!("Meteora DLMM API 测试失败: {}", e);
                panic!("测试失败");
            }
        }
    }

    #[tokio::test]
    async fn test_all_apis() {
        let result = run_all_api_tests().await;
        assert!(result.is_ok(), "所有API测试应该成功");
    }
}