use serde::{Deserialize, Serialize};
use serde_json::Value;
use std::collections::HashMap;
use std::error::Error;

#[derive(Serialize, Deserialize, Debug)]
pub struct JupiterMarket {
    pub address: String,
    pub owner: String,
    pub params: Option<Value>,
    pub addressLookupTableAddress: Option<String>,
}

#[derive(Deserialize)]
struct RaydiumV3Pool {
    id: String,
    #[serde(rename = "programId")]
    program_id: String,
    #[serde(rename = "type")]
    pool_type: String,
}

#[derive(Deserialize)]
struct MeteoraPool {
    address: String,
}

#[derive(Deserialize)]
struct RaydiumV2Pool {
    id: String,
    #[serde(rename = "lookupTableAccount")]
    lookup_table_account: String,
}

pub struct JupiterFormatter;

impl JupiterFormatter {
    pub fn new() -> Self {
        Self
    }

    pub fn format_raydium_v3_pools(&self, pools_data: &str) -> Result<Vec<JupiterMarket>, Box<dyn Error>> {
        let data: Value = serde_json::from_str(pools_data)?;
        let pools = data["data"]["data"].as_array().ok_or("Invalid Raydium V3 data structure")?;
        
        let mut markets = Vec::new();
        
        for pool in pools {
            let pool_obj: RaydiumV3Pool = serde_json::from_value(pool.clone())?;
            
            let market = JupiterMarket {
                address: pool_obj.id,
                owner: pool_obj.program_id,
                params: None,
                addressLookupTableAddress: None,
            };
            
            markets.push(market);
        }
        
        Ok(markets)
    }

    pub fn format_meteora_dlmm_pools(&self, pools_data: &str) -> Result<Vec<JupiterMarket>, Box<dyn Error>> {
        let pools: Vec<MeteoraPool> = serde_json::from_str(pools_data)?;
        
        let mut markets = Vec::new();
        
        for pool in pools {
            let market = JupiterMarket {
                address: pool.address,
                owner: "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo".to_string(), // Meteora DLMM Program ID
                params: None,
                addressLookupTableAddress: None,
            };
            
            markets.push(market);
        }
        
        Ok(markets)
    }

    pub fn format_raydium_v2_clmm_pools(&self, pools_data: &str) -> Result<Vec<JupiterMarket>, Box<dyn Error>> {
        let pools: Vec<RaydiumV2Pool> = serde_json::from_str(pools_data)?;
        
        let mut markets = Vec::new();
        
        for pool in pools {
            let lookup_table = if pool.lookup_table_account == "11111111111111111111111111111111" {
                None
            } else {
                Some(pool.lookup_table_account)
            };
            
            let market = JupiterMarket {
                address: pool.id,
                owner: "CAMMCzo5YL8w4VFF8KVHrK22GGUsp5VTaW7grrKgrWqK".to_string(), // Raydium CLMM Program ID
                params: None,
                addressLookupTableAddress: lookup_table,
            };
            
            markets.push(market);
        }
        
        Ok(markets)
    }

    pub async fn submit_to_jupiter(&self, markets: &[JupiterMarket]) -> Result<(), Box<dyn Error>> {
        let client = reqwest::Client::new();
        
        for market in markets {
            let response = client
                .post("http://*************:8083/add-market")
                .json(market)
                .send()
                .await?;
            
            if response.status().is_success() {
                println!("✓ Successfully submitted market: {}", market.address);
            } else {
                println!("✗ Failed to submit market: {} - Status: {}", market.address, response.status());
                let error_text = response.text().await.unwrap_or_default();
                if !error_text.is_empty() {
                    println!("  Error: {}", error_text);
                }
            }
        }
        
        Ok(())
    }

    pub fn print_market_details(&self, markets: &[JupiterMarket], source: &str) {
        println!("\n{} Markets ({} total):", source, markets.len());
        println!("{:-<80}", "");
        
        for (i, market) in markets.iter().enumerate() {
            println!("{}. Address: {}", i + 1, market.address);
            println!("   Owner: {}", market.owner);
            if let Some(params) = &market.params {
                println!("   Params: {:?}", params);
            }
            if let Some(lookup_table) = &market.addressLookupTableAddress {
                println!("   Lookup Table: {}", lookup_table);
            }
            println!();
        }
    }
}