use crate::config::{Config, DexApiConfig};
use crate::types::*;
use anyhow::{Context, Result};
use reqwest::Client;
use std::time::Duration;
use tokio::time::sleep;
use tracing::{debug, info, warn};

pub struct DexClient {
    client: Client,
    config: Config,
}

impl DexClient {
    pub fn new(config: Config) -> Self {
        let client = Client::builder()
            .timeout(Duration::from_secs(30))
            .build()
            .expect("Failed to create HTTP client");

        Self { client, config }
    }

    /// 获取所有启用DEX的池信息
    pub async fn get_all_pools(&self, mint_pairs: &[MintPair]) -> Result<Vec<PoolInfo>> {
        let mut all_pools = Vec::new();
        let enabled_dexs = self.config.enabled_dex_apis();

        for (dex_name, dex_config) in enabled_dexs {
            info!("正在查询 {} 的池信息...", dex_name);
            
            match self.get_pools_for_dex(&dex_name, dex_config, mint_pairs).await {
                Ok(pools) => {
                    info!("{} 找到 {} 个池", dex_name, pools.len());
                    all_pools.extend(pools);
                }
                Err(e) => {
                    warn!("{} 查询失败: {}", dex_name, e);
                }
            }

            // 添加延迟以避免API限流
            sleep(Duration::from_millis(500)).await;
        }

        info!("总共找到 {} 个池", all_pools.len());
        Ok(all_pools)
    }

    /// 为特定DEX获取池信息
    async fn get_pools_for_dex(
        &self,
        dex_name: &str,
        dex_config: &DexApiConfig,
        mint_pairs: &[MintPair],
    ) -> Result<Vec<PoolInfo>> {
        match dex_name {
            "raydium_v3" => self.get_raydium_v3_pools(dex_config, mint_pairs).await,
            "raydium_v2" => self.get_raydium_v2_pools(dex_config, mint_pairs).await,
            "meteora_dlmm" => self.get_meteora_dlmm_pools(dex_config, mint_pairs).await,
            "meteora_amm" => self.get_meteora_amm_pools(dex_config, mint_pairs).await,
            _ => {
                warn!("未知的DEX: {}", dex_name);
                Ok(Vec::new())
            }
        }
    }

    /// 获取Raydium V3池信息
    async fn get_raydium_v3_pools(
        &self,
        config: &DexApiConfig,
        mint_pairs: &[MintPair],
    ) -> Result<Vec<PoolInfo>> {
        let mut pools = Vec::new();

        for pair in mint_pairs {
            let url = format!(
                "{}/pools/info/mint?mint1={}&mint2={}&poolType=all&poolSortField=liquidity&sortType=desc&pageSize=100&page=1",
                config.base_url, pair.base_mint, pair.quote_mint
            );

            debug!("请求Raydium V3: {}", url);

            let response = self
                .client
                .get(&url)
                .timeout(Duration::from_secs(config.timeout_secs))
                .send()
                .await
                .with_context(|| format!("Raydium V3 API请求失败: {}", url))?;

            if response.status().is_success() {
                let raydium_response: RaydiumV3Response = response
                    .json()
                    .await
                    .with_context(|| "解析Raydium V3响应失败")?;

                if raydium_response.success {
                    for pool in raydium_response.data.data {
                        // 使用正确的TVL字段作为流动性指标
                        let liquidity_usd = pool.tvl;
                        
                        let volume_24h_usd = pool.day
                            .as_ref()
                            .and_then(|d| d.volume_quote);

                        pools.push(PoolInfo {
                            address: pool.id,
                            owner: pool.program_id,
                            base_mint: pool.mint_a.address,
                            quote_mint: pool.mint_b.address,
                            dex_name: "Raydium V3".to_string(),
                            pool_type: Some(pool.pool_type),
                            liquidity_usd,
                            volume_24h_usd,
                        });
                    }
                }
            } else {
                warn!("Raydium V3 API返回错误: {}", response.status());
            }

            // API限流延迟
            sleep(Duration::from_millis(1000 / config.rate_limit_per_sec as u64)).await;
        }

        Ok(pools)
    }

    /// 获取Raydium V2 CLMM池信息
    async fn get_raydium_v2_pools(
        &self,
        config: &DexApiConfig,
        mint_pairs: &[MintPair],
    ) -> Result<Vec<PoolInfo>> {
        let url = format!("{}/ammV3/ammPools", config.base_url);
        debug!("请求Raydium V2: {}", url);

        let response = self
            .client
            .get(&url)
            .timeout(Duration::from_secs(config.timeout_secs))
            .send()
            .await
            .with_context(|| "Raydium V2 API请求失败")?;

        if !response.status().is_success() {
            warn!("Raydium V2 API返回错误: {}", response.status());
            return Ok(Vec::new());
        }

        let pools_data: serde_json::Value = response
            .json()
            .await
            .with_context(|| "解析Raydium V2响应失败")?;

        let pools_array = pools_data["data"].as_array()
            .context("Raydium V2响应格式错误")?;

        let mut matching_pools = Vec::new();

        for pool_value in pools_array {
            let pool: RaydiumV2Pool = serde_json::from_value(pool_value.clone())
                .context("解析Raydium V2池信息失败")?;

            // 检查是否匹配任何mint对
            for pair in mint_pairs {
                if (pool.mint_a == pair.base_mint && pool.mint_b == pair.quote_mint) ||
                   (pool.mint_a == pair.quote_mint && pool.mint_b == pair.base_mint) {
                    
                    let _lookup_table = if pool.lookup_table_account.as_ref()
                        .map_or(false, |addr| addr != "11111111111111111111111111111111") {
                        pool.lookup_table_account
                    } else {
                        None
                    };

                    matching_pools.push(PoolInfo {
                        address: pool.id,
                        owner: config.program_id.as_ref()
                            .unwrap_or(&"CAMMCzo5YL8w4VFF8KVHrK22GGUsp5VTaW7grrKgrWqK".to_string())
                            .clone(),
                        base_mint: pair.base_mint.clone(),
                        quote_mint: pair.quote_mint.clone(),
                        dex_name: "Raydium V2 CLMM".to_string(),
                        pool_type: Some("CLMM".to_string()),
                        liquidity_usd: pool.tvl,
                        volume_24h_usd: None,
                    });
                    break;
                }
            }
        }

        Ok(matching_pools)
    }

    /// 获取Meteora DLMM池信息
    async fn get_meteora_dlmm_pools(
        &self,
        config: &DexApiConfig,
        mint_pairs: &[MintPair],
    ) -> Result<Vec<PoolInfo>> {
        let url = format!("{}/pair/all", config.base_url);
        debug!("请求Meteora DLMM: {}", url);

        let response = self
            .client
            .get(&url)
            .timeout(Duration::from_secs(config.timeout_secs))
            .send()
            .await
            .with_context(|| "Meteora DLMM API请求失败")?;

        if !response.status().is_success() {
            warn!("Meteora DLMM API返回错误: {}", response.status());
            return Ok(Vec::new());
        }

        let pools: Vec<MeteoraPool> = response
            .json()
            .await
            .with_context(|| "解析Meteora DLMM响应失败")?;

        let mut matching_pools = Vec::new();

        for pair in mint_pairs {
            for pool in &pools {
                if (pool.mint_x == pair.base_mint && pool.mint_y == pair.quote_mint) ||
                   (pool.mint_x == pair.quote_mint && pool.mint_y == pair.base_mint) {
                    
                    // 正确获取Meteora的流动性和交易量数据
                    let liquidity_usd = pool.liquidity
                        .as_ref()
                        .and_then(|l| l.parse::<f64>().ok());
                    
                    let volume_24h_usd = pool.trade_volume_24h;

                    matching_pools.push(PoolInfo {
                        address: pool.address.clone(),
                        owner: config.program_id.as_ref()
                            .unwrap_or(&"LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo".to_string())
                            .clone(),
                        base_mint: pair.base_mint.clone(),
                        quote_mint: pair.quote_mint.clone(),
                        dex_name: "Meteora DLMM".to_string(),
                        pool_type: pool.bin_step.map(|step| format!("DLMM (bin_step: {})", step)),
                        liquidity_usd,
                        volume_24h_usd,
                    });
                }
            }
        }

        Ok(matching_pools)
    }

    /// 获取Meteora AMM池信息
    async fn get_meteora_amm_pools(
        &self,
        _config: &DexApiConfig,
        _mint_pairs: &[MintPair],
    ) -> Result<Vec<PoolInfo>> {
        // Meteora AMM API需要地址参数，这里返回空结果
        // 实际实现需要根据具体的API文档进行调整
        debug!("Meteora AMM API暂不支持批量查询");
        Ok(Vec::new())
    }
}