use anyhow::{Context, Result};
use serde::Deserialize;
use std::collections::HashMap;
use std::fs;

#[derive(Debug, Clone, Deserialize)]
pub struct Config {
    pub jupiter: JupiterConfig,
    pub dex_apis: HashMap<String, DexApiConfig>,
    pub processing: ProcessingConfig,
    pub logging: LoggingConfig,
    #[serde(default)]
    pub filters: FilterConfig,
}

#[derive(Debug, <PERSON>lone, Deserialize)]
pub struct JupiterConfig {
    pub api_urls: Vec<String>,
    pub request_delay_ms: u64,
    pub batch_size: usize,
}

#[derive(Debug, Clone, Deserialize)]
pub struct DexApiConfig {
    pub enabled: bool,
    pub base_url: String,
    pub rate_limit_per_sec: u32,
    pub timeout_secs: u64,
    #[serde(default)]
    pub program_ids: Vec<String>,
    #[serde(default)]
    pub program_id: Option<String>,
}

#[derive(Debug, <PERSON>lone, Deserialize)]
pub struct ProcessingConfig {
    pub csv_file_path: String,
    pub max_concurrent_requests: usize,
}

#[derive(Debug, Clone, Deserialize)]
pub struct LoggingConfig {
    pub level: String,
    pub success_log_file: String,
    pub error_log_file: String,
}

#[derive(Debug, Clone, Deserialize)]
pub struct FilterConfig {
    /// 是否启用流动性过滤
    #[serde(default = "default_true")]
    pub enable_liquidity_filter: bool,
    /// 最低流动性要求（美元）
    #[serde(default = "default_min_liquidity")]
    pub min_liquidity_usd: f64,
    /// 是否要求必须有流动性数据
    #[serde(default = "default_false")]
    pub require_liquidity_data: bool,
    
    /// 是否启用交易量过滤
    #[serde(default = "default_true")]
    pub enable_volume_filter: bool,
    /// 最低24小时交易量要求（美元）
    #[serde(default = "default_min_volume")]
    pub min_volume_24h_usd: f64,
    /// 是否要求必须有交易量数据
    #[serde(default = "default_false")]
    pub require_volume_data: bool,
    
    /// 是否启用黑名单过滤
    #[serde(default = "default_true")]
    pub enable_blacklist_filter: bool,
    /// 黑名单代币列表
    #[serde(default)]
    pub blacklisted_tokens: Vec<String>,
    
    /// 是否启用程序ID过滤
    #[serde(default = "default_false")]
    pub enable_program_id_filter: bool,
    /// 支持的程序ID列表
    #[serde(default)]
    pub supported_program_ids: Vec<String>,
    
    /// 质量分级阈值
    #[serde(default)]
    pub quality_tiers: QualityTiers,
}

#[derive(Debug, Clone, Deserialize)]
pub struct QualityTiers {
    /// 高级池流动性阈值
    #[serde(default = "default_premium_liquidity")]
    pub premium_liquidity_usd: f64,
    /// 高级池交易量阈值
    #[serde(default = "default_premium_volume")]
    pub premium_volume_24h_usd: f64,
    /// 推荐池流动性阈值
    #[serde(default = "default_recommended_liquidity")]
    pub recommended_liquidity_usd: f64,
    /// 推荐池交易量阈值
    #[serde(default = "default_recommended_volume")]
    pub recommended_volume_24h_usd: f64,
}

// 默认值函数
fn default_true() -> bool { true }
fn default_false() -> bool { false }
fn default_min_liquidity() -> f64 { 10_000.0 }
fn default_min_volume() -> f64 { 5_000.0 }
fn default_premium_liquidity() -> f64 { 100_000.0 }
fn default_premium_volume() -> f64 { 50_000.0 }
fn default_recommended_liquidity() -> f64 { 50_000.0 }
fn default_recommended_volume() -> f64 { 20_000.0 }

impl Default for FilterConfig {
    fn default() -> Self {
        Self {
            enable_liquidity_filter: true,
            min_liquidity_usd: 10_000.0,
            require_liquidity_data: false,
            enable_volume_filter: true,
            min_volume_24h_usd: 5_000.0,
            require_volume_data: false,
            enable_blacklist_filter: true,
            blacklisted_tokens: Vec::new(),
            enable_program_id_filter: false,
            supported_program_ids: Vec::new(),
            quality_tiers: QualityTiers::default(),
        }
    }
}

impl Default for QualityTiers {
    fn default() -> Self {
        Self {
            premium_liquidity_usd: 100_000.0,
            premium_volume_24h_usd: 50_000.0,
            recommended_liquidity_usd: 50_000.0,
            recommended_volume_24h_usd: 20_000.0,
        }
    }
}

impl Config {
    /// 从TOML文件加载配置
    pub fn load(config_path: &str) -> Result<Self> {
        let config_content = fs::read_to_string(config_path)
            .with_context(|| format!("无法读取配置文件: {}", config_path))?;
        
        let config: Config = toml::from_str(&config_content)
            .with_context(|| "配置文件格式错误")?;
        
        config.validate()?;
        
        Ok(config)
    }
    
    /// 验证配置的有效性
    fn validate(&self) -> Result<()> {
        if self.jupiter.api_urls.is_empty() {
            anyhow::bail!("Jupiter API URLs不能为空");
        }
        
        if self.processing.csv_file_path.is_empty() {
            anyhow::bail!("CSV文件路径不能为空");
        }
        
        if self.processing.max_concurrent_requests == 0 {
            anyhow::bail!("最大并发请求数必须大于0");
        }
        
        Ok(())
    }
    
    /// 获取启用的DEX API配置
    pub fn enabled_dex_apis(&self) -> HashMap<String, &DexApiConfig> {
        self.dex_apis
            .iter()
            .filter(|(_, config)| config.enabled)
            .map(|(name, config)| (name.clone(), config))
            .collect()
    }
    
    /// 获取DEX的程序ID列表
    pub fn get_program_ids(&self, dex_name: &str) -> Vec<String> {
        if let Some(config) = self.dex_apis.get(dex_name) {
            if !config.program_ids.is_empty() {
                config.program_ids.clone()
            } else if let Some(ref program_id) = config.program_id {
                vec![program_id.clone()]
            } else {
                Vec::new()
            }
        } else {
            Vec::new()
        }
    }
}