use crate::config::JupiterConfig;
use crate::types::{JupiterMarket, PoolInfo, SubmissionResult};
use anyhow::{Context, Result};
use reqwest::Client;
use std::time::Duration;
use tokio::time::sleep;
use tracing::{debug, info, warn};

pub struct JupiterClient {
    client: Client,
    config: JupiterConfig,
}

impl JupiterClient {
    pub fn new(config: JupiterConfig) -> Self {
        let client = Client::builder()
            .timeout(Duration::from_secs(30))
            .build()
            .expect("Failed to create HTTP client");

        Self { client, config }
    }

    /// 将池信息转换为Jupiter格式
    pub fn convert_to_jupiter_format(&self, pool: &PoolInfo) -> JupiterMarket {
        JupiterMarket {
            address: pool.address.clone(),
            owner: pool.owner.clone(),
            params: None,
            address_lookup_table_address: None,
        }
    }

    /// 批量提交市场到所有Jupiter实例
    pub async fn submit_markets(&self, pools: Vec<PoolInfo>) -> Result<Vec<SubmissionResult>> {
        let mut all_results = Vec::new();
        
        info!("开始提交 {} 个市场到 {} 个Jupiter实例", 
              pools.len(), self.config.api_urls.len());

        // 转换为Jupiter格式
        let jupiter_markets: Vec<JupiterMarket> = pools
            .iter()
            .map(|pool| self.convert_to_jupiter_format(pool))
            .collect();

        // 提交到每个Jupiter实例
        for (idx, api_url) in self.config.api_urls.iter().enumerate() {
            info!("正在提交到Jupiter实例 {} ({})", idx + 1, api_url);
            
            let results = self.submit_to_single_jupiter(api_url, &jupiter_markets).await?;
            all_results.extend(results);

            // 实例间延迟
            if idx < self.config.api_urls.len() - 1 {
                sleep(Duration::from_millis(self.config.request_delay_ms)).await;
            }
        }

        Ok(all_results)
    }

    /// 提交到单个Jupiter实例
    async fn submit_to_single_jupiter(
        &self,
        api_url: &str,
        markets: &[JupiterMarket],
    ) -> Result<Vec<SubmissionResult>> {
        let mut results = Vec::new();
        
        // 分批处理
        for (batch_idx, chunk) in markets.chunks(self.config.batch_size).enumerate() {
            debug!("处理第 {} 批，包含 {} 个市场", batch_idx + 1, chunk.len());
            
            for market in chunk {
                let result = self.submit_single_market(api_url, market).await;
                results.push(result);

                // 请求间延迟
                sleep(Duration::from_millis(self.config.request_delay_ms)).await;
            }
        }

        Ok(results)
    }

    /// 提交单个市场
    async fn submit_single_market(
        &self,
        api_url: &str,
        market: &JupiterMarket,
    ) -> SubmissionResult {
        debug!("提交市场: {} 到 {}", market.address, api_url);

        match self.do_submit(api_url, market).await {
            Ok(_) => {
                debug!("✓ 成功提交市场: {}", market.address);
                SubmissionResult {
                    market: market.clone(),
                    success: true,
                    error: None,
                    jupiter_url: api_url.to_string(),
                }
            }
            Err(e) => {
                warn!("✗ 提交市场失败: {} - 错误: {}", market.address, e);
                SubmissionResult {
                    market: market.clone(),
                    success: false,
                    error: Some(e.to_string()),
                    jupiter_url: api_url.to_string(),
                }
            }
        }
    }

    /// 执行实际的提交请求
    async fn do_submit(&self, api_url: &str, market: &JupiterMarket) -> Result<()> {
        let response = self
            .client
            .post(api_url)
            .json(market)
            .send()
            .await
            .with_context(|| format!("请求Jupiter API失败: {}", api_url))?;

        if response.status().is_success() {
            Ok(())
        } else {
            let status = response.status();
            let error_text = response
                .text()
                .await
                .unwrap_or_else(|_| "无法获取错误信息".to_string());
            
            anyhow::bail!("Jupiter API返回错误: {} - {}", status, error_text);
        }
    }

    /// 验证提交是否成功（通过quote测试）
    pub async fn verify_submission(
        &self,
        base_mint: &str,
        quote_mint: &str,
        api_url: &str,
    ) -> Result<bool> {
        // 构建quote测试URL
        let quote_url = api_url.replace("/add-market", "/quote");
        let test_url = format!(
            "{}?inputMint={}&outputMint={}&amount=1000000&slippageBps=50",
            quote_url, base_mint, quote_mint
        );

        debug!("验证提交结果: {}", test_url);

        match self.client.get(&test_url).send().await {
            Ok(response) => {
                if response.status().is_success() {
                    let quote_response: serde_json::Value = response.json().await?;
                    // 如果能获取到路由计划，说明市场已成功添加
                    Ok(quote_response.get("routePlan").is_some())
                } else {
                    Ok(false)
                }
            }
            Err(_) => Ok(false),
        }
    }
}