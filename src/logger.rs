use crate::config::LoggingConfig;
use crate::types::{ProcessingReport, SubmissionResult};
use anyhow::Result;
use std::fs::OpenOptions;
use std::io::Write;
use tracing_subscriber::{fmt, EnvFilter};

pub struct Logger {
    config: LoggingConfig,
}

impl Logger {
    pub fn new(config: LoggingConfig) -> Self {
        Self { config }
    }

    /// 初始化日志系统
    pub fn init(&self) -> Result<()> {
        let filter = EnvFilter::try_from_default_env()
            .unwrap_or_else(|_| EnvFilter::new(&self.config.level));

        tracing_subscriber::fmt()
            .with_env_filter(filter)
            .with_target(false)
            .with_thread_ids(true)
            .with_file(true)
            .with_line_number(true)
            .init();

        Ok(())
    }

    /// 记录成功的提交
    pub fn log_success(&self, result: &SubmissionResult) -> Result<()> {
        let mut file = OpenOptions::new()
            .create(true)
            .append(true)
            .open(&self.config.success_log_file)?;

        let log_entry = format!(
            "[{}] SUCCESS - Market: {} | Owner: {} | Jupiter: {}\n",
            chrono::Utc::now().format("%Y-%m-%d %H:%M:%S"),
            result.market.address,
            result.market.owner,
            result.jupiter_url
        );

        file.write_all(log_entry.as_bytes())?;
        file.flush()?;

        Ok(())
    }

    /// 记录失败的提交
    pub fn log_error(&self, result: &SubmissionResult) -> Result<()> {
        let mut file = OpenOptions::new()
            .create(true)
            .append(true)
            .open(&self.config.error_log_file)?;

        let log_entry = format!(
            "[{}] ERROR - Market: {} | Owner: {} | Jupiter: {} | Error: {}\n",
            chrono::Utc::now().format("%Y-%m-%d %H:%M:%S"),
            result.market.address,
            result.market.owner,
            result.jupiter_url,
            result.error.as_ref().unwrap_or(&"Unknown error".to_string())
        );

        file.write_all(log_entry.as_bytes())?;
        file.flush()?;

        Ok(())
    }

    /// 记录处理报告
    pub fn log_report(&self, report: &ProcessingReport) -> Result<()> {
        let mut success_file = OpenOptions::new()
            .create(true)
            .append(true)
            .open(&self.config.success_log_file)?;

        let report_entry = format!(
            "\n[{}] PROCESSING REPORT:\n\
            - Total mint pairs: {}\n\
            - Total pools found: {}\n\
            - Total submissions: {}\n\
            - Successful submissions: {}\n\
            - Failed submissions: {}\n\
            - Success rate: {:.2}%\n\n",
            chrono::Utc::now().format("%Y-%m-%d %H:%M:%S"),
            report.total_mint_pairs,
            report.total_pools_found,
            report.total_submissions,
            report.successful_submissions,
            report.failed_submissions,
            if report.total_submissions > 0 {
                (report.successful_submissions as f64 / report.total_submissions as f64) * 100.0
            } else {
                0.0
            }
        );

        success_file.write_all(report_entry.as_bytes())?;
        success_file.flush()?;

        Ok(())
    }

    /// 批量记录结果
    pub fn log_batch_results(&self, results: &[SubmissionResult]) -> Result<()> {
        for result in results {
            if result.success {
                self.log_success(result)?;
            } else {
                self.log_error(result)?;
            }
        }
        Ok(())
    }
}